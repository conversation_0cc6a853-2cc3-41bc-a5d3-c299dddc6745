// @mui
import { Typography, CardContent } from '@mui/material';
// components
import Logo from 'src/components/shared/logo';
import { CustomCard } from 'src/components/shared/cards';

// ----------------------------------------------------------------------

export default function HeroCard() {
  return (
    <CustomCard width="100%" height={1} blueCard>
      <CardContent
        sx={{
          py: 4,
          px: 7,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <Logo sx={{ width: 64, height: 64, mb: 2 }} />
        <Typography variant="h1" textAlign="center" sx={{ fontWeight: 700, mb: 1, color: '#fff' }}>
          Your Ai Heath Assistant
        </Typography>
      </CardContent>
    </CustomCard>
  );
}
