import { lazy, Suspense } from 'react';
import { Outlet } from 'react-router-dom';
// guard
import { AuthGuard } from 'src/guard';
// layouts
import DashboardLayout from 'src/layouts/dashboard';
// components
import { LoadingScreen } from 'src/components/shared/loading-screen';

// ----------------------------------------------------------------------

const Home = lazy(() => import('src/pages/dashboard/home'));

// ----------------------------------------------------------------------

export const dashboardRoutes = [
  {
    path: 'dashboard',
    element: (
      <AuthGuard>
        <DashboardLayout>
          <Suspense fallback={<LoadingScreen />}>
            <Outlet />
          </Suspense>
        </DashboardLayout>
      </AuthGuard>
    ),
    children: [{ element: <Home />, index: true }],
  },
];
