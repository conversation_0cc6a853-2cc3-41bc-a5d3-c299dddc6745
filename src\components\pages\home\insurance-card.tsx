// @mui
import { Box, Stack, Typography, CardContent } from '@mui/material';
// components
import { CustomCard } from 'src/components/shared/cards';

// ----------------------------------------------------------------------

export default function InsuranceCard() {
  return (
    <CustomCard height="100%">
      <Box
        sx={{
          height: 1,
          position: 'relative',
          '&:before': {
            content: '""',
            position: 'absolute',
            top: '-5px',
            left: '-5px',
            right: 0,
            bottom: 0,
            width: '100%',
            height: '100%',
            background: "url('/assets/particle/radial_white.svg')",
            backgroundRepeat: 'no-repeat',
            backgroundPosition: 'top left',
            zIndex: 0,
          },
        }}
      >
        <CardContent
          sx={{
            p: 3,
            height: '100%',
            position: 'relative',
            zIndex: 1,
          }}
        >
          <Stack spacing={2} height={1} alignItems="center" justifyContent="center">
            <Typography
              variant="h1"
              sx={{
                background: 'linear-gradient(123deg, #FFF -2.63%, #3A42D8 104.28%)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
              }}
            >
              45
            </Typography>
            <Typography variant="subtitle1" color="secondary.main" textAlign="center">
              Insurance of the contracting party
            </Typography>
          </Stack>
        </CardContent>
      </Box>
    </CustomCard>
  );
}
