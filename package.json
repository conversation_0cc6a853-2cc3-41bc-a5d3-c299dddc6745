{"name": "<PERSON><PERSON><PERSON><PERSON>", "author": "<PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>", "version": "5.6.0", "description": "Vite Starter & TypeScript", "private": true, "type": "module", "scripts": {"dev": "vite", "start": "vite preview", "build": "tsc && vite build", "lint": "eslint \"src/**/*.{js,jsx,ts,tsx}\"", "lint:fix": "eslint --fix \"src/**/*.{js,jsx,ts,tsx}\"", "prettier": "prettier --write \"src/**/*.{js,jsx,ts,tsx}\"", "rm:all": "rm -rf node_modules .next out dist build", "re:start": "yarn rm:all && yarn install && yarn dev", "re:build": "yarn rm:all && yarn install && yarn build", "re:build-npm": "npm run rm:all && npm install && npm run build", "dev:host": "vite --host"}, "dependencies": {"@auth0/auth0-react": "^2.2.1", "@emotion/cache": "^11.11.0", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@hookform/resolvers": "^3.3.1", "@iconify/react": "^4.1.1", "@mui/icons-material": "^5.17.1", "@mui/lab": "^5.0.0-alpha.147", "@mui/material": "^5.14.12", "autosuggest-highlight": "^3.3.4", "axios": "^1.5.1", "date-fns": "^2.30.0", "framer-motion": "^10.16.4", "history": "^5.3.0", "lodash": "^4.17.21", "nprogress": "^0.2.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-helmet-async": "^1.3.0", "react-hook-form": "^7.47.0", "react-lazy-load-image-component": "^1.6.3", "react-router": "^6.16.0", "react-router-dom": "^6.16.0", "simplebar-react": "^3.2.4", "stylis": "^4.3.0", "stylis-plugin-rtl": "^2.1.1", "yup": "^1.3.2"}, "devDependencies": {"@types/autosuggest-highlight": "^3.2.0", "@types/lodash": "^4.14.199", "@types/node": "^20.8.2", "@types/nprogress": "^0.2.1", "@types/numeral": "^2.0.3", "@types/react": "^18.2.25", "@types/react-dom": "^18.2.10", "@types/react-lazy-load-image-component": "^1.6.4", "@types/stylis": "^4.2.1", "@typescript-eslint/eslint-plugin": "^6.7.4", "@typescript-eslint/parser": "^6.7.4", "@vitejs/plugin-react-swc": "^3.4.0", "eslint": "^8.50.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^17.1.0", "eslint-config-prettier": "^9.0.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-perfectionist": "^2.1.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-unused-imports": "^3.0.0", "prettier": "^3.0.3", "typescript": "^5.2.2", "vite": "^6.3.2", "vite-plugin-checker": "^0.6.2"}}