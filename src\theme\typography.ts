// ----------------------------------------------------------------------

export function remToPx(value: string) {
  return Math.round(parseFloat(value) * 16);
}

export function pxToRem(value: number) {
  return `${value / 16}rem`;
}

export function responsiveFontSizes({ sm, md, lg }: { sm: number; md: number; lg: number }) {
  return {
    '@media (min-width:600px)': {
      fontSize: pxToRem(sm),
    },
    '@media (min-width:900px)': {
      fontSize: pxToRem(md),
    },
    '@media (min-width:1200px)': {
      fontSize: pxToRem(lg),
    },
  };
}

declare module '@mui/material/styles' {
  interface TypographyVariants {
    fontSecondaryFamily: React.CSSProperties['fontFamily'];
    fontWeightSemiBold: React.CSSProperties['fontWeight'];
  }
}

export const primaryFont = 'Gellix, Arial, sans-serif';
export const secondaryFont = 'Barlow, sans-serif';

// ----------------------------------------------------------------------

export const typography = {
  fontFamily: primaryFont,
  fontSecondaryFamily: secondaryFont,
  fontWeightRegular: 400,
  fontWeightMedium: 500,
  fontWeightSemiBold: 600,
  fontWeightBold: 700,
  h1: {
    fontWeight: 600,
    lineHeight: 1,
    fontSize: pxToRem(54),
  },
  h2: {
    fontWeight: 700,
    lineHeight: 1,
    fontSize: pxToRem(30),
  },
  h3: {
    fontWeight: 700,
    lineHeight: 1,
    fontSize: pxToRem(30),
  },
  h4: {
    fontWeight: 600,
    lineHeight: 1,
    fontSize: pxToRem(28),
  },
  h5: {
    fontWeight: 400,
    lineHeight: 1,
    fontSize: pxToRem(20),
  },
  h6: {
    fontWeight: 600,
    lineHeight: 1,
    fontSize: pxToRem(18),
  },
  subtitle1: {
    fontWeight: 400,
    lineHeight: 1,
    fontSize: pxToRem(16),
  },
  subtitle2: {
    fontWeight: 400,
    lineHeight: 1,
    fontSize: pxToRem(14),
  },
  body1: {
    lineHeight: 1,
    fontSize: pxToRem(16),
  },
  body2: {
    lineHeight: 1,
    fontSize: pxToRem(14),
  },
  caption: {
    lineHeight: 1,
    fontSize: pxToRem(12),
  },
  overline: {
    fontWeight: 700,
    lineHeight: 1,
    fontSize: pxToRem(12),
    textTransform: 'uppercase',
  },
  button: {
    fontWeight: 700,
    lineHeight: 1,
    fontSize: pxToRem(14),
    textTransform: 'unset',
  },
} as const;
