// @mui
import { Stack, Typography, CardContent } from '@mui/material';
import Logo from 'src/components/shared/logo';
// components
import { CustomCard } from 'src/components/shared/cards';

// ----------------------------------------------------------------------

export default function LiveSubtitlesCard() {
  return (
    <CustomCard height="100%" backgroundImageExtra="/assets/background/bg_live_subtitle_card.png">
      <CardContent
        sx={{
          p: { xs: 2, md: 3 },
          display: 'flex',
          flexDirection: 'column',
          height: '100%',
        }}
      >
        <Stack height={1} justifyContent="space-between">
          <Stack spacing={3}>
            <Logo />
            <Typography
              variant="h3"
              sx={{
                background: 'linear-gradient(123deg, #FFF -2.63%, #3A42D8 104.28%)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                pr: 1,
              }}
            >
              Live Subtitles for Doctors & Patients
            </Typography>
          </Stack>
          <Stack spacing={1}>
            <Typography variant="h6">Every Where</Typography>
            <Typography variant="subtitle1" color="secondary.main">
              Every Doctor you Want
            </Typography>
          </Stack>
        </Stack>
      </CardContent>
    </CustomCard>
  );
}
