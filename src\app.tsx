/* eslint-disable perfectionist/sort-imports */
import 'src/global.css';

// ----------------------------------------------------------------------

import Router from 'src/routes/pages';
// hooks
import { useScrollToTop } from 'src/hooks/use-scroll-to-top';
// theme
import ThemeProvider from 'src/theme';
// components
import ProgressBar from 'src/components/shared/progress-bar';
import { MotionLazy } from 'src/components/shared/animate/motion-lazy';
import { SettingsDrawer } from 'src/components/shared/settings';
// providers
import { AuthProvider } from 'src/context/jwt/auth-provider';
import { SettingsProvider } from './context/settings';

// ----------------------------------------------------------------------

export default function App() {
  useScrollToTop();

  return (
    <AuthProvider>
      <SettingsProvider
        defaultSettings={{
          themeMode: 'light', // 'light' | 'dark'
          themeDirection: 'ltr', //  'rtl' | 'ltr'
          themeContrast: 'default', // 'default' | 'bold'
          themeLayout: 'vertical', // 'vertical' | 'horizontal' | 'mini'
          themeColorPresets: 'default', // 'default' | 'cyan' | 'purple' | 'blue' | 'orange' | 'red'
          themeStretch: false,
        }}
      >
        <ThemeProvider>
          <MotionLazy>
            <SettingsDrawer />
            <ProgressBar />
            <Router />
          </MotionLazy>
        </ThemeProvider>
      </SettingsProvider>
    </AuthProvider>
  );
}
