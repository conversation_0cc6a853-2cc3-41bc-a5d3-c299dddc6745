import { forwardRef } from 'react';
// @mui
import Box from '@mui/material/Box';
import Link from '@mui/material/Link';
import Tooltip from '@mui/material/Tooltip';
import { styled, useTheme } from '@mui/material/styles';
import ListItemButton from '@mui/material/ListItemButton';
// routes
import { RouterLink } from 'src/routes/general';
// components
import Iconify from '../../iconify';
// types
import { NavItemProps, NavItemStateProps } from '../types';

// ----------------------------------------------------------------------

const NavItem = forwardRef<HTMLDivElement, NavItemProps>(
  (
    {
      title,
      path,
      icon,
      info,
      disabled,
      caption,
      roles,
      //
      open,
      depth,
      active,
      hasChild,
      externalLink,
      currentRole = 'admin',
      ...other
    },
    ref
  ) => {
    const theme = useTheme();
    const subItem = depth !== 1;

    const renderContent = (
      <StyledNavItem
        ref={ref}
        disableGutters
        open={open}
        depth={depth}
        active={active}
        disabled={disabled}
        {...other}
      >
        {!subItem && icon && (
          <Box component="span" className="icon">
            {icon}
          </Box>
        )}

        {subItem && icon ? (
          <Box component="span" className="icon">
            {icon}
          </Box>
        ) : (
          subItem && !icon && <Box component="span" className="sub-icon" />
        )}

        {title && (
          <Box component="span" sx={{ flex: '1 1 auto', minWidth: 0 }}>
            <Box component="span" className="label">
              {title}
            </Box>

            {caption && (
              <Tooltip title={caption} placement="top-start">
                <Box component="span" className="caption">
                  {caption}
                </Box>
              </Tooltip>
            )}
          </Box>
        )}

        {!subItem && ( // Active indicator dot container
          <Box
            component="span"
            className="active-indicator"
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              position: 'absolute',
              top: '50%',
              right: -20, // Positions the dot outside the navbar border
              zIndex: 10, // Ensures dot is above the curve pseudo-elements
              opacity: active ? 1 : 0,
              transform: active
                ? 'translateY(-50%) translateX(0)'
                : 'translateY(-50%) translateX(10px)',
              transition: theme.transitions.create(['opacity', 'transform'], {
                duration: theme.transitions.duration.short,
                easing: theme.transitions.easing.easeInOut,
              }),
            }}
          >
            <Box // The dot itself
              sx={{
                width: '40px',
                height: '40px',
                // This SVG is responsible for the dot's appearance.
                background: `url('/assets/icons/navbar/bullet_menu_active.svg') no-repeat center center`,
                backgroundSize: 'contain',
              }}
            />
          </Box>
        )}

        {info && (
          <Box component="span" className="info">
            {info}
          </Box>
        )}

        {hasChild && (
          <Iconify
            width={16}
            className="arrow"
            icon={open ? 'eva:arrow-ios-downward-fill' : 'eva:arrow-ios-forward-fill'}
          />
        )}
      </StyledNavItem>
    );

    if (roles && !roles.includes(`${currentRole}`)) {
      return null;
    }

    if (hasChild) {
      return renderContent;
    }

    if (externalLink)
      return (
        <Link
          href={path}
          target="_blank"
          rel="noopener"
          color="inherit"
          underline="none"
          sx={{ ...(disabled && { cursor: 'default' }) }}
        >
          {renderContent}
        </Link>
      );

    return (
      <Link
        component={RouterLink}
        href={path}
        color="inherit"
        underline="none"
        sx={{ ...(disabled && { cursor: 'default' }) }}
      >
        {renderContent}
      </Link>
    );
  }
);

NavItem.displayName = 'NavItem';
export default NavItem;

// ----------------------------------------------------------------------

export const StyledNavItem = styled(ListItemButton, {
  shouldForwardProp: (prop) => prop !== 'active' && prop !== 'depth' && prop !== 'open',
})<NavItemStateProps>(({ active, open, depth, theme }) => {
  const subItem = depth !== 1;
  const opened = open && !active;

  const navPanelBackground = '#1a1d29'; // Explicit background color for the curve - matches navbar gradient

  const commonPseudoStyles = {
    content: '""',
    position: 'absolute',
    height: '25px', // Half of the total curve height
    backgroundColor: navPanelBackground, // Crucial: same as nav panel
    zIndex: 5, // Below the dot, but above default content
    right: 0, // Aligns with the item's right edge
    opacity: 0,
    width: 0, // Starts with no width
    transition: theme.transitions.create(['opacity', 'width'], {
      duration: theme.transitions.duration.standard,
      easing: theme.transitions.easing.easeInOut,
    }),
  };

  const baseItemStyles: Record<string, any> = {
    // Added type for baseItemStyles
    marginBottom: 4,
    borderRadius: 8,
    color: 'rgba(255, 255, 255, 0.7)', // Light gray text for inactive items
    padding: theme.spacing(0.5, 1, 0.5, 1.5),
    minHeight: 44,
    position: 'relative',
  };

  // Add overflow: 'visible' for root items specifically
  if (!subItem) {
    baseItemStyles.overflow = 'visible';
  }

  const styles: Record<string, any> = {
    ...baseItemStyles,

    '&:hover': {
      backgroundColor: 'rgba(255, 255, 255, 0.05)',
      color: 'rgba(255, 255, 255, 0.9)',
    },

    '& .icon': {
      width: 24,
      height: 24,
      flexShrink: 0,
      marginRight: theme.spacing(2),
    },
    '& .label': {
      width: '100%',
      maxWidth: '100%',
      display: 'block',
      overflow: 'hidden',
      whiteSpace: 'nowrap',
      textOverflow: 'ellipsis',
      ...theme.typography.body2,
      textTransform: 'capitalize',
      fontWeight: active ? theme.typography.fontWeightSemiBold : theme.typography.fontWeightMedium,
    },
    '& .caption': {
      width: '100%',
      maxWidth: '100%',
      display: 'block',
      overflow: 'hidden',
      whiteSpace: 'nowrap',
      textOverflow: 'ellipsis',
      ...theme.typography.caption,
      color: theme.palette.text.disabled,
    },
    '& .info': {
      display: 'inline-flex',
      marginLeft: theme.spacing(0.75),
    },
    '& .arrow': {
      flexShrink: 0,
      marginLeft: theme.spacing(0.75),
    },

    ...(!subItem && {
      // Styles for top-level items
      '&::before': {
        // Top part of the curve
        ...commonPseudoStyles,
        top: 'calc(50% - 25px)', // Positions at the vertical midpoint, then up by its height
        borderBottomLeftRadius: '25px', // Creates the curve
      },
      '&::after': {
        // Bottom part of the curve
        ...commonPseudoStyles,
        bottom: 'calc(50% - 25px)', // Positions at the vertical midpoint, then down by its height
        borderTopLeftRadius: '25px', // Creates the curve
      },
    }),
  };

  if (!subItem) {
    if (active) {
      styles.color = '#ffffff'; // White text for active items
      styles.paddingRight = theme.spacing(4);
      styles['&:hover'] = { backgroundColor: 'transparent' };

      styles['&::before'] = {
        ...styles['&::before'],
        opacity: 1,
        width: '25px', // This is the width of the curve indentation
      };
      styles['&::after'] = {
        ...styles['&::after'],
        opacity: 1,
        width: '25px', // This is the width of the curve indentation
      };
    } else if (opened) {
      styles.color = theme.palette.text.primary;
      styles.backgroundColor = theme.palette.action.hover;
    }
  } else {
    styles.minHeight = 36;
    styles.padding = theme.spacing(0.5, 1);

    styles['& .sub-icon'] = {
      width: 24,
      height: 24,
      flexShrink: 0,
      marginRight: theme.spacing(2),
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      '&:before': {
        content: '""',
        width: 4,
        height: 4,
        borderRadius: '50%',
        backgroundColor: theme.palette.text.disabled,
        transition: theme.transitions.create(['transform'], {
          duration: theme.transitions.duration.shorter,
        }),
        ...(active && {
          transform: 'scale(2)',
          backgroundColor: theme.palette.primary.main,
        }),
      },
    };
    if (active) {
      styles.color = theme.palette.text.primary;
      styles['&:hover'] = { backgroundColor: 'transparent' };
    }
    if (Number(depth) > 2) {
      styles.paddingLeft = theme.spacing(Number(depth));
    }
  }
  return styles;
});
