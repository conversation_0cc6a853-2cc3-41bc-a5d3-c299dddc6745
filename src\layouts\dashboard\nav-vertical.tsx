import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
// @mui
import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import Drawer from '@mui/material/Drawer';
import Typography from '@mui/material/Typography';
// routes
// hooks
import { usePathname } from 'src/hooks/use-pathname';
import { useResponsive } from 'src/hooks/use-responsive';
import { useMockedUser } from 'src/hooks/use-mocked-user';
// components
import Logo from 'src/components/shared/logo';
import Scrollbar from 'src/components/shared/scrollbar';
import { NavSectionVertical } from 'src/components/shared/nav-section';
// common
import { NAV } from '../config-layout';
import { useNavData, useMediPathNavData } from './config-navigation';
import NavToggleButton from '../common/nav-toggle-button';

// ----------------------------------------------------------------------

type Props = {
  openNav: boolean;
  onCloseNav: VoidFunction;
};

export default function NavVertical({ openNav, onCloseNav }: Props) {
  const { user } = useMockedUser();
  const { pathname } = useLocation();
  const lgUp = useResponsive('up', 'lg');

  const isMediPath = pathname.startsWith('/dashboard/medipath');
  const navData = isMediPath ? useMediPathNavData() : useNavData();

  useEffect(() => {
    if (openNav) {
      onCloseNav();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pathname]);

  const renderMediPathLogo = (
    <Box sx={{ px: 2.5, py: 3, display: 'flex', alignItems: 'center', gap: 1.5 }}>
      <Box
        sx={{
          width: 32,
          height: 32,
          borderRadius: '8px',
          background: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
          <path
            d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"
            fill="white"
            opacity="0.3"
          />
          <path
            d="M12 6c-3.31 0-6 2.69-6 6s2.69 6 6 6c1.66 0 3.16-.67 4.24-1.76l-1.41-1.41C14.03 15.63 13.07 16 12 16c-2.21 0-4-1.79-4-4s1.79-4 4-4c.93 0 1.79.32 2.47.86l1.42-1.42C14.84 6.67 13.49 6 12 6z"
            fill="white"
          />
          <circle cx="12" cy="12" r="2" fill="white" />
        </svg>
      </Box>
      <Typography
        variant="h6"
        sx={{
          color: '#ffffff',
          fontWeight: 600,
          fontSize: '1.125rem',
        }}
      >
        MediPath
      </Typography>
      <Box
        sx={{
          backgroundColor: '#6366f1',
          color: '#ffffff',
          borderRadius: '4px',
          px: 1,
          py: 0.25,
          fontSize: '0.75rem',
          fontWeight: 600,
        }}
      >
        AI
      </Box>
    </Box>
  );

  const renderContent = (
    <Scrollbar
      sx={{
        height: 1,
        '& .simplebar-content': {
          height: 1,
          display: 'flex',
          flexDirection: 'column',
        },
      }}
    >
      {isMediPath ? renderMediPathLogo : <Logo sx={{ mt: 3, ml: 4, mb: 1 }} />}

      <NavSectionVertical
        data={navData}
        slotProps={{
          currentRole: user?.role,
        }}
      />

      <Box sx={{ flexGrow: 1 }} />
    </Scrollbar>
  );

  const mediPathSx = isMediPath
    ? {
        background: 'linear-gradient(180deg, #1a1d29 0%, #0f1117 100%)',
        borderRight: 'none',
      }
    : {};

  return (
    <Box
      sx={{
        flexShrink: { lg: 0 },
        width: { lg: NAV.W_VERTICAL },
      }}
    >
      <NavToggleButton />

      {lgUp ? (
        <Stack
          sx={{
            height: 1,
            position: 'fixed',
            width: NAV.W_VERTICAL,
            borderRight: (theme) => `dashed 1px ${theme.palette.divider}`,
            ...mediPathSx,
          }}
        >
          {renderContent}
        </Stack>
      ) : (
        <Drawer
          open={openNav}
          onClose={onCloseNav}
          PaperProps={{
            sx: {
              width: NAV.W_VERTICAL,
              ...mediPathSx,
            },
          }}
        >
          {renderContent}
        </Drawer>
      )}
    </Box>
  );
}
