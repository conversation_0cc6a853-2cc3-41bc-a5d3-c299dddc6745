import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
// @mui
import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import Drawer from '@mui/material/Drawer';
import Typography from '@mui/material/Typography';
// routes
// hooks
import { useResponsive } from 'src/hooks/use-responsive';
import { useMockedUser } from 'src/hooks/use-mocked-user';
// components
import Logo from 'src/components/shared/logo';
import Scrollbar from 'src/components/shared/scrollbar';
import { NavSectionVertical } from 'src/components/shared/nav-section';
// common
import { NAV } from '../config-layout';
import { useNavData } from './config-navigation';

// ----------------------------------------------------------------------

type Props = {
  openNav: boolean;
  onCloseNav: VoidFunction;
};

export default function NavVertical({ openNav, onCloseNav }: Props) {
  const { user } = useMockedUser();
  const { pathname } = useLocation();
  const lgUp = useResponsive('up', 'lg');

  const isDashboard = pathname.startsWith('/dashboard');
  const navData = useNavData();

  useEffect(() => {
    if (openNav) {
      onCloseNav();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pathname]);

  const renderMediPathLogo = (
    <Box sx={{ px: 2.5, py: 3, display: 'flex', alignItems: 'center', gap: 1.5 }}>
      <Logo />
      <Typography
        variant="h6"
        sx={{
          color: '#ffffff',
          fontWeight: 600,
          fontSize: '16px',
        }}
      >
        MediPath
      </Typography>
      <Box
        sx={{
          backgroundColor: '#6366f1',
          color: '#ffffff',
          borderRadius: '4px',
          px: 1,
          py: 0.25,
          fontSize: '9px',
          fontWeight: 600,
        }}
      >
        Plus
      </Box>
    </Box>
  );

  const renderContent = (
    <Scrollbar
      sx={{
        height: 1,
        '& .simplebar-content': {
          height: 1,
          display: 'flex',
          flexDirection: 'column',
        },
      }}
    >
      {isDashboard ? renderMediPathLogo : <Logo sx={{ mt: 3, ml: 4, mb: 1 }} />}

      <NavSectionVertical
        data={navData}
        slotProps={{
          currentRole: user?.role,
        }}
      />

      <Box sx={{ flexGrow: 1 }} />
    </Scrollbar>
  );

  const dashboardSx = isDashboard
    ? {
        background: '#03061C',
        borderRight: '1px solid rgba(255, 255, 255, 0.15)',
      }
    : {};

  return (
    <Box
      sx={{
        flexShrink: { lg: 0 },
        width: { lg: NAV.W_VERTICAL },
      }}
    >
      {lgUp ? (
        <Stack
          sx={{
            height: 1,
            position: 'fixed',
            width: NAV.W_VERTICAL,
            borderRight: (theme) => `dashed 1px ${theme.palette.divider}`,
            ...dashboardSx,
          }}
        >
          {renderContent}
        </Stack>
      ) : (
        <Drawer
          open={openNav}
          onClose={onCloseNav}
          PaperProps={{
            sx: {
              width: NAV.W_VERTICAL,
              ...dashboardSx,
            },
          }}
        >
          {renderContent}
        </Drawer>
      )}
    </Box>
  );
}
