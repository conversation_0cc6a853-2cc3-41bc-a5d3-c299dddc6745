@font-face {
  font-display: swap;
  font-family: 'Gellix';
  font-weight: 100;
  font-style: normal;
  src: url('/fonts/Gellix-Thin.woff2') format('woff2');
}

@font-face {
  font-display: swap;
  font-family: 'Gellix';
  font-weight: 300;
  font-style: normal;
  src: url('/fonts/Gellix-Light.woff2') format('woff2');
}

@font-face {
  font-display: swap;
  font-family: 'Gellix';
  font-weight: 400;
  font-style: normal;
  src: url('/fonts/Gellix-Regular.woff2') format('woff2');
}

@font-face {
  font-display: swap;
  font-family: 'Gellix';
  font-weight: 500;
  font-style: normal;
  src: url('/fonts/Gellix-Medium.woff2') format('woff2');
}

@font-face {
  font-display: swap;
  font-family: 'Gellix';
  font-weight: 600;
  font-style: normal;
  src: url('/fonts/Gellix-SemiBold.woff2') format('woff2');
}

@font-face {
  font-display: swap;
  font-family: 'Gellix';
  font-weight: 700;
  font-style: normal;
  src: url('/fonts/Gellix-Bold.woff2') format('woff2');
}

@font-face {
  font-display: swap;
  font-family: 'Gellix';
  font-weight: 800;
  font-style: normal;
  src: url('/fonts/Gellix-ExtraBold.woff2') format('woff2');
}

@font-face {
  font-display: swap;
  font-family: 'Gellix';
  font-weight: 900;
  font-style: normal;
  src: url('/fonts/Gellix-Black.woff2') format('woff2');
}

