// @mui
import { Stack, Typography, CardContent } from '@mui/material';
// components
import Image from 'src/components/shared/image';
import { CustomCard } from 'src/components/shared/cards';

// ----------------------------------------------------------------------

export default function VoiceTranslationCard() {
  return (
    <CustomCard height="100%">
      <CardContent
        sx={{
          p: 3,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'flex-start',
          justifyContent: 'center',
          height: '100%',
        }}
      >
        <Image
          src="/assets/particle/particle_real_time_card.png"
          alt="particle1"
          style={{ position: 'absolute', top: 0, right: 0, zIndex: 1 }}
        />
        <Stack height={1} justifyContent="flex-end">
          <Stack spacing={0.5}>
            <Typography variant="h6" lineHeight="24px">
              Real-Time Voice Translation
            </Typography>
            <Typography variant="subtitle1" color="secondary.main">
              Doctor-Patient Conversations
            </Typography>
          </Stack>
        </Stack>
      </CardContent>
    </CustomCard>
  );
}
