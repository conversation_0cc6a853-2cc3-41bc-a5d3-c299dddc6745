import React from 'react';
// @mui
import Card from '@mui/material/Card';
import { Theme, SxProps } from '@mui/material/styles';

// ----------------------------------------------------------------------

interface CustomCardProps {
  children: React.ReactNode;
  width?: string | number;
  height?: string | number;
  sx?: SxProps<Theme>;
  blueCard?: boolean;
  backgroundImageExtra?: string;
}

// ----------------------------------------------------------------------

const CustomCard: React.FC<CustomCardProps> = ({
  children,
  width = '100%',
  height = 'fit-content',
  sx = {},
  blueCard = false,
  backgroundImageExtra,
}) => (
  <Card
    sx={{
      borderRadius: '20px',
      border: '0.856px solid inside',
      borderColor: blueCard ? 'rgba(255,255,255,0.2)' : 'rgba(255,255,255,0.08)',
      position: 'relative',
      width,
      height,
      boxShadow: 'none',
      overflow: 'hidden',
      '&:before': {
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: -1,
        width: '100%',
        height: '100%',
        // eslint-disable-next-line no-nested-ternary
        background: blueCard
          ? "url('/assets/background/background_card_blue.svg') 0% 0% / 100% 100%"
          : backgroundImageExtra
          ? `url(${backgroundImageExtra}) bottom/100% auto no-repeat`
          : 'radial-gradient(146.4% 139.7% at 97.54% 0%, rgba(58, 66, 216, 0.30) 0%, rgba(58, 66, 216, 0.00) 100%)',
        backgroundPosition: blueCard || backgroundImageExtra ? 'bottom' : undefined,
        // eslint-disable-next-line no-nested-ternary
        backgroundSize: blueCard ? 'cover' : backgroundImageExtra ? '100% auto' : undefined,
        backgroundRepeat: blueCard || backgroundImageExtra ? 'no-repeat' : undefined,
        // eslint-disable-next-line no-nested-ternary
        backgroundColor: blueCard ? 'transparent' : backgroundImageExtra ? 'unset' : '#191934',
        backgroundBlendMode: !blueCard ? 'overlay, normal' : 'overlay, normal',
        borderRadius: '20px',
      },
      '&:after': {
        content: '""',
        position: 'absolute',
        top: 0,
        right: 0,
        width: '100%',
        height: '100%',
        zIndex: -1,
        pointerEvents: 'none',
        background: 'linear-gradient(270deg, rgba(58,66,216,0.18) 0%, rgba(58,66,216,0) 50%)',
        borderTopRightRadius: '20px',
        borderBottomRightRadius: '20px',
      },
      ...sx,
    }}
  >
    {children}
  </Card>
);

export default CustomCard;
