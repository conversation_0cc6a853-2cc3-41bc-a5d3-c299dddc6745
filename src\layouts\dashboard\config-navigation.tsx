import { useMemo } from 'react';
// icons
import {
  Home as HomeIcon,
  Chat as ChatIcon,
  Help as HelpIcon,
  Login as LoginIcon,
  Logout as LogoutIcon,
  Article as ArticleIcon,
  LightMode as LightModeIcon,
  PersonAdd as PersonAddIcon,
  LocalHospital as LocalHospitalIcon,
  MedicalServices as MedicalServicesIcon,
} from '@mui/icons-material';
// components
import SvgColor from 'src/components/shared/svg-color';

// ----------------------------------------------------------------------

const icon = (name: string) => (
  <SvgColor src={`/assets/icons/navbar/${name}.svg`} sx={{ width: 1, height: 1 }} />
  // OR
  // <Iconify icon="fluent:mail-24-filled" />
  // https://icon-sets.iconify.design/solar/
  // https://www.streamlinehq.com/icons
);

const ICONS = {
  job: icon('ic_job'),
  blog: icon('ic_blog'),
  chat: icon('ic_chat'),
  mail: icon('ic_mail'),
  user: icon('ic_user'),
  file: icon('ic_file'),
  lock: icon('ic_lock'),
  tour: icon('ic_tour'),
  order: icon('ic_order'),
  label: icon('ic_label'),
  blank: icon('ic_blank'),
  kanban: icon('ic_kanban'),
  folder: icon('ic_folder'),
  banking: icon('ic_banking'),
  booking: icon('ic_booking'),
  invoice: icon('ic_invoice'),
  product: icon('ic_product'),
  calendar: icon('ic_calendar'),
  disabled: icon('ic_disabled'),
  external: icon('ic_external'),
  menuItem: icon('ic_menu_item'),
  ecommerce: icon('ic_ecommerce'),
  analytics: icon('ic_analytics'),
  dashboard: icon('ic_dashboard'),
  // MediPath icons
  home: <HomeIcon />,
  medical: <MedicalServicesIcon />,
  hospital: <LocalHospitalIcon />,
  chatIcon: <ChatIcon />,
  article: <ArticleIcon />,
  lightMode: <LightModeIcon />,
  login: <LoginIcon />,
  personAdd: <PersonAddIcon />,
  help: <HelpIcon />,
  logout: <LogoutIcon />,
};

// ----------------------------------------------------------------------

export function useNavData() {
  const data = useMemo(
    () => [
      // MAIN NAVIGATION
      // ----------------------------------------------------------------------
      {
        subheader: 'Main',
        items: [
          { title: 'Home', path: '/dashboard', icon: ICONS.home },
          { title: 'Doctors', path: '/dashboard/doctors', icon: ICONS.medical },
          { title: 'Hospitals', path: '/dashboard/hospitals', icon: ICONS.hospital },
          { title: 'Chat', path: '/dashboard/chat', icon: ICONS.chatIcon },
          { title: 'Blog', path: '/dashboard/blog', icon: ICONS.article },
        ],
      },

      // SETTINGS & ACCOUNT
      // ----------------------------------------------------------------------
      {
        subheader: 'Account',
        items: [
          { title: 'Light Mode', path: '#', icon: ICONS.lightMode },
          { title: 'Login', path: '/auth/login', icon: ICONS.login },
          { title: 'Sign Up', path: '/auth/register', icon: ICONS.personAdd },
          { title: 'Updates & FAQ', path: '/dashboard/medipath/faq', icon: ICONS.help },
          { title: 'Log out', path: '/auth/logout', icon: ICONS.logout },
        ],
      },
    ],
    []
  );

  return data;
}
