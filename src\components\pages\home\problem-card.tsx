// @mui
import { Stack, Typography, CardContent } from '@mui/material';
// components
import Image from 'src/components/shared/image';
import { CustomCard } from 'src/components/shared/cards';

// ----------------------------------------------------------------------

export default function ProblemCard() {
  return (
    <CustomCard height="100%" backgroundImageExtra="/assets/background/bg_problem_card.png">
      <CardContent
        sx={{
          p: 5,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'flex-start',
          height: '100%',
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        {/* Particles absolutely positioned */}

        <Image
          src="/assets/particle/particle_problem_card_2.png"
          alt="particle2"
          style={{ position: 'absolute', bottom: 120, left: 94, zIndex: 1 }}
        />
        <Image
          src="/assets/particle/particle_problem_card_3.png"
          alt="particle3"
          style={{ position: 'absolute', bottom: 60, left: 18, zIndex: 1 }}
        />
        <Image
          src="/assets/particle/particle_problem_card_4.png"
          alt="particle4"
          style={{ position: 'absolute', bottom: 70, left: 94, zIndex: 1 }}
        />
        <Image
          src="/assets/particle/particle_problem_card_5.png"
          alt="particle5"
          style={{ position: 'absolute', bottom: 22, left: 16, zIndex: 1 }}
        />
        <Image
          src="/assets/particle/particle_problem_card_6.png"
          alt="particle6"
          style={{ position: 'absolute', bottom: 42, left: 150, zIndex: 1 }}
        />
        <Image
          src="/assets/particle/particle_problem_card_7.png"
          alt="particle7"
          style={{ position: 'absolute', bottom: 75, left: 295, zIndex: 1 }}
        />
        <Image
          src="/assets/particle/particle_problem_card_8.png"
          alt="particle8"
          style={{ position: 'absolute', bottom: 32, left: 260, zIndex: 1 }}
        />
        <Stack width={1} direction="row" alignItems="flex-start">
          <Stack spacing={1} maxWidth="55%">
            <Typography variant="h6">Just the Say your Problem</Typography>
            <Typography variant="subtitle1" color="secondary.main">
              Our doctors will be available right away.
            </Typography>
          </Stack>
          <Image
            src="/assets/particle/particle_problem_card_1.png"
            width="126px"
            height="60px"
            alt="particle1"
            sx={{ transform: 'translateY(-10px)' }}
          />
        </Stack>
      </CardContent>
    </CustomCard>
  );
}
