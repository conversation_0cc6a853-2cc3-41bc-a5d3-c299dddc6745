// @mui
import { Box, Stack, Avatar, Typography, AvatarGroup, CardContent } from '@mui/material';
// component
import { CustomCard } from 'src/components/shared/cards';

// ----------------------------------------------------------------------

const userAvatars = [
  'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=150&h=150&fit=crop&crop=face',
  'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
  'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
  'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
];

// ----------------------------------------------------------------------

export default function HappyUserCard() {
  return (
    <CustomCard height={1}>
      <CardContent
        sx={{
          p: 4,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'flex-start',
          justifyContent: 'flex-start',
        }}
      >
        <Stack spacing={2}>
          <Typography
            variant="h1"
            sx={{
              background: 'linear-gradient(123deg, #FFF -2.63%, #3A42D8 104.28%)',
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
            }}
          >
            12k
          </Typography>
          <Typography variant="subtitle1" color="secondary.main">
            happy users
          </Typography>
        </Stack>
        {userAvatars && (
          <Box sx={{ mt: 2 }}>
            <AvatarGroup
              spacing="small"
              max={4}
              sx={{
                '& .MuiAvatar-root': {
                  width: 50,
                  height: 50,
                  border: '2px solid #1C1E3C',
                },
              }}
            >
              {userAvatars.map((avatar, index) => (
                <Avatar
                  key={index}
                  src={avatar}
                  sx={{ width: 50, height: 50, zIndex: index - 1 }}
                />
              ))}
            </AvatarGroup>
          </Box>
        )}
      </CardContent>
    </CustomCard>
  );
}
