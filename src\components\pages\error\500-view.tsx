import { m } from 'framer-motion';
// @mui
import { Button, Typography } from '@mui/material';
// routes
import { RouterLink } from 'src/routes/general';
// assets
import { SeverErrorIllustration } from 'src/assets/illustrations';
// componenets
import { varBounce, MotionContainer } from 'src/components/shared/animate';

// ----------------------------------------------------------------------

export default function Page500() {
  return (
    <MotionContainer>
      <m.div variants={varBounce().in}>
        <Typography variant="h3" sx={{ mb: 2 }}>
          500 Internal Server Error
        </Typography>
      </m.div>

      <m.div variants={varBounce().in}>
        <Typography sx={{ color: 'text.secondary' }}>
          There was an error, please try again later.
        </Typography>
      </m.div>

      <m.div variants={varBounce().in}>
        <SeverErrorIllustration sx={{ height: 260, my: { xs: 5, sm: 10 } }} />
      </m.div>

      <Button component={RouterLink} href="/" size="large" variant="contained">
        Go to Home
      </Button>
    </MotionContainer>
  );
}
