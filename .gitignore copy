# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules
.pnp
.pnp.js

# testing
coverage

# production
.next
.swc
_static
out
dist
build

# environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# misc
.DS_Store
.vercel
.netlify
.unimportedrc.json
tsconfig.tsbuildinfo
.vscode

npm-debug.log*
yarn-debug.log*
yarn-error.log*
