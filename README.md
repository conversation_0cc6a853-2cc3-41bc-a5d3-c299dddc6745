# Boilerplate - React TypeScript - ViteJs

A modern React TypeScript starter template built with Vite, featuring Material-UI components and comprehensive tooling.

## 🚀 Features

- **React 18** with TypeScript
- **Vite** for fast development and building
- **Material-UI v5** components
- **JWT Authentication**
- **React Router v6**
- **React Hook Form** for form handling
- **Responsive Layout** (vertical, horizontal, and mini variants)
- **RTL Support**
- **Theme Customization**
- **Dark/Light Mode**

## 🔧 Prerequisites

- Node.js 16.x or 18.x
- yarn (recommended) or npm

## 🛠 Installation

### Using Yarn (Recommended)

```bash
# Install dependencies
yarn install

# Start development server
yarn dev

# Build for production
yarn build
```

### Using NPM

```bash
# Install dependencies
npm i
# or if you encounter peer dependency issues
npm i --legacy-peer-deps

# Start development server
npm run dev

# Build for production
npm run build
```

## 📁 Project Structure

```
src/
├── app.tsx              # Main application component
├── main.tsx            # Application entry point
├── components/         # Reusable components
│   ├── shared/        # Common shared components
│   └── pages/         # Page-specific components
├── layouts/           # Layout components
│   ├── dashboard/     # Dashboard layout
│   └── common/        # Common layout elements
├── routes/            # Route configurations
│   └── pages/         # Page routes
├── theme/             # Theme configuration
├── context/           # React context providers
└── _mock/            # Mock data for development

public/               # Static assets
```

## 🔌 Available Scripts

- `yarn dev` or `npm run dev` - Start development server
- `yarn build` or `npm run build` - Build for production
- `yarn start` or `npm run start` - Preview production build
- `yarn lint` or `npm run lint` - Run ESLint
- `yarn lint:fix` or `npm run lint:fix` - Fix ESLint errors
- `yarn prettier` or `npm run prettier` - Format code
- `yarn dev:host` or `npm run dev:host` - Start dev server with host network access

## 🎨 Theme Customization

The theme can be customized through the following settings:

- **Theme Mode**: `light` | `dark`
- **Theme Direction**: `ltr` | `rtl`
- **Theme Contrast**: `default` | `bold`
- **Theme Layout**: `vertical` | `horizontal` | `mini`
- **Theme Color**: `default` | `cyan` | `purple` | `blue` | `orange` | `red`
- **Theme Stretch**: `true` | `false`

## 🔒 Authentication

The project uses JWT authentication with the following features:

- Login/Register functionality
- Protected routes
- Auth context provider
- Token management

## 🌐 Routing

Routes are configured in `src/routes/pages/index.tsx` with the following structure:

- Auth routes
- Dashboard routes
- Main routes
- 404 handling

## 📱 Responsive Design

The application is fully responsive with:

- Multiple layout options
- Responsive navigation
- Mobile-friendly components
- Adaptive content display

## 🛠 Development Tools

- **TypeScript** for type safety
- **ESLint** for code linting
- **Prettier** for code formatting
- **Vite** for development and building
- **React Router** for navigation
- **Material-UI** for UI components

## 🔍 Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## 📄 License

This project is licensed under the MIT License.

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request
