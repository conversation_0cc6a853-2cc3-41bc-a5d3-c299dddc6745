import { useEffect, useCallback } from 'react';
// routes
import { paths } from 'src/routes/paths';
// hooks
import { useRouter } from 'src/hooks/use-router';
import { useAuthContext } from 'src/hooks/use-auth-context';
import { useSearchParams } from 'src/hooks/use-search-params';
// components
import { SplashScreen } from 'src/components/shared/loading-screen';

// ----------------------------------------------------------------------

type Props = {
  children: React.ReactNode;
};

export default function GuestGuard({ children }: Props) {
  const { loading } = useAuthContext();

  return <>{loading ? <SplashScreen /> : <Container>{children}</Container>}</>;
}

// ----------------------------------------------------------------------

function Container({ children }: Props) {
  const router = useRouter();

  const searchParams = useSearchParams();

  const returnTo = searchParams.get('returnTo') || paths.dashboard.root;

  const { authenticated } = useAuthContext();

  const check = useCallback(() => {
    if (authenticated) {
      router.replace(returnTo);
    }
  }, [authenticated, returnTo, router]);

  useEffect(() => {
    check();
  }, [check]);

  return <>{children}</>;
}
