// @mui
import { Stack, Typography, CardContent } from '@mui/material';
// component
import { CustomCard } from 'src/components/shared/cards';

// ----------------------------------------------------------------------

export default function ExpertDoctorCard() {
  return (
    <CustomCard height="80%">
      <CardContent
        sx={{
          height: 1,
          p: 4,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <Stack spacing={3}>
          <Typography
            variant="h1"
            sx={{
              background: 'linear-gradient(123deg, #FFF -2.63%, #3A42D8 104.28%)',
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
            }}
          >
            2.5k
          </Typography>
          <Typography color="secondary.main" variant="subtitle1">
            Expert Doctors
          </Typography>
        </Stack>
      </CardContent>
    </CustomCard>
  );
}
