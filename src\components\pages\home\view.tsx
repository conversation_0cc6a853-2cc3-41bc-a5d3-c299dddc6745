import { useState } from 'react';
import { Mic as MicIcon, Send as SendIcon } from '@mui/icons-material';
import { Box, Grid, Card, Container, IconButton, CardContent } from '@mui/material';
import HeroCard from './hero-card';
import ProblemCard from './problem-card';
import InsuranceCard from './insurance-card';
import HappyUserCard from './happy-user-card';
import ExpertDoctorCard from './expert-doctor-card';
import LiveSubtitlesCard from './live-subtitles-card';
import VoiceTranslationCard from './voice-translation-card';

const ChatInput = () => {
  const [message, setMessage] = useState('');

  return (
    <Card
      sx={{
        position: 'relative',
        background: 'transparent',
        borderRadius: '16px',
        mx: { lg: 'auto' },
        zIndex: 1000,
      }}
    >
      <CardContent sx={{ p: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 3 }}>
          <IconButton size="small" sx={{ color: 'rgba(255, 255, 255, 0.7)' }}>
            <MicIcon fontSize="small" />
          </IconButton>
          <Box
            component="input"
            placeholder="Type message"
            value={message}
            onChange={(e: any) => setMessage(e.target.value)}
            sx={{
              flex: 1,
              border: 'none',
              outline: 'none',
              background: 'transparent',
              color: '#ffffff',
              fontSize: '0.875rem',
              '&::placeholder': {
                color: 'rgba(255, 255, 255, 0.5)',
              },
            }}
          />

          <IconButton size="small" sx={{ color: message ? '#6366f1' : 'rgba(255, 255, 255, 0.3)' }}>
            <SendIcon fontSize="small" />
          </IconButton>
        </Box>
      </CardContent>
    </Card>
  );
};

export default function Home() {
  return (
    <Box
      sx={{
        minHeight: '100vh',
      }}
    >
      <Container maxWidth="xl">
        <Box
          component="div"
          sx={{
            height: '80vh',
          }}
        >
          <Grid container spacing={3} sx={{ height: '100%' }}>
            {/* Main Grid 9 - Left Side */}
            <Grid
              item
              xs={12}
              md={9}
              sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}
            >
              {/* Top Row - Grid 3 + Grid 9 */}
              <Grid container spacing={3} sx={{ flex: 1 }}>
                {/* Grid 3 - Stats Cards Column */}
                <Grid
                  item
                  xs={12}
                  md={3.5}
                  sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}
                >
                  <ExpertDoctorCard />
                  <HappyUserCard />
                </Grid>
                {/* Grid 9 - Hero Card */}
                <Grid item xs={12} md={8.5}>
                  <HeroCard />
                </Grid>
              </Grid>
              {/* Bottom Row - Grid 9 + Grid 3 */}
              <Grid container spacing={3} sx={{ flex: 1, mt: 0 }}>
                {/* Grid 9 - Problem Card */}
                <Grid item xs={12} md={8}>
                  <ProblemCard />
                </Grid>
                {/* Grid 3 - Voice Translation */}
                <Grid item xs={12} md={4}>
                  <VoiceTranslationCard />
                </Grid>
              </Grid>
            </Grid>
            {/* Right Grid 3 - 75% + 25% */}
            <Grid
              item
              xs={12}
              md={3}
              sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}
            >
              {/* 75% - Live Subtitles */}
              <Box sx={{ flex: 3, mb: 3 }}>
                <LiveSubtitlesCard />
              </Box>
              {/* 25% - Insurance */}
              <Box sx={{ flex: 1 }}>
                <InsuranceCard />
              </Box>
            </Grid>
          </Grid>
        </Box>
        <Box
          sx={{
            height: '10vh',
            mt: 8,
          }}
        >
          <ChatInput />
        </Box>
      </Container>
    </Box>
  );
}
