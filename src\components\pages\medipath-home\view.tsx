import { useState } from 'react';
import {
  Mic as Mic<PERSON>con,
  Send as SendIcon,
  AttachFile as AttachFileIcon,
} from '@mui/icons-material';
import {
  Box,
  Card,
  Avatar,
  Container,
  Typography,
  IconButton,
  AvatarGroup,
  CardContent,
} from '@mui/material';

// Mock data for avatars
const userAvatars = [
  'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=150&h=150&fit=crop&crop=face',
  'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
  'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
  'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
];

// Custom Card Components
const StatsCard = ({
  value,
  label,
  avatars,
}: {
  value: string;
  label: string;
  avatars?: string[];
}) => (
  <Card
    sx={{
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      borderRadius: '20px',
      border: '0.856px solid rgba(255,255,255,0.05)',
      background:
        'radial-gradient(146.4% 139.7% at 97.54% 0%, rgba(58, 66, 216, 0.30) 0%, rgba(58, 66, 216, 0.00) 100%), #191934',
      backgroundBlendMode: 'overlay, normal, normal',
      height: '100%',
      position: 'relative',
      overflow: 'hidden',
    }}
  >
    <CardContent
      sx={{
        p: 3,
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between',
      }}
    >
      <Box>
        <Typography
          variant="h3"
          sx={{ color: '#ffffff', fontWeight: 700, fontSize: '2.5rem', lineHeight: 1.2, mb: 0.5 }}
        >
          {value}
        </Typography>
        <Typography
          variant="body2"
          sx={{ color: 'rgba(255, 255, 255, 0.7)', fontSize: '0.875rem', fontWeight: 400 }}
        >
          {label}
        </Typography>
      </Box>
      {avatars && (
        <Box sx={{ mt: 2 }}>
          <AvatarGroup
            max={4}
            sx={{
              '& .MuiAvatar-root': {
                width: 32,
                height: 32,
                border: '2px solid rgba(255, 255, 255, 0.2)',
              },
            }}
          >
            {avatars.map((avatar, index) => (
              <Avatar key={index} src={avatar} sx={{ width: 32, height: 32 }} />
            ))}
          </AvatarGroup>
        </Box>
      )}
    </CardContent>
  </Card>
);

const HeroCard = () => (
  <Card
    sx={{
      background: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)',
      borderRadius: '20px',
      height: '100%',
      position: 'relative',
      overflow: 'hidden',
      border: 'none',
      boxShadow: '0 8px 32px rgba(99, 102, 241, 0.3)',
    }}
  >
    <CardContent
      sx={{
        p: 3,
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        textAlign: 'center',
      }}
    >
      <Box sx={{ mb: 3 }}>
        <Box
          sx={{
            width: 64,
            height: 64,
            borderRadius: '16px',
            background: 'rgba(255, 255, 255, 0.2)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
            <path
              d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"
              fill="white"
              opacity="0.3"
            />
            <path
              d="M12 6c-3.31 0-6 2.69-6 6s2.69 6 6 6c1.66 0 3.16-.67 4.24-1.76l-1.41-1.41C14.03 15.63 13.07 16 12 16c-2.21 0-4-1.79-4-4s1.79-4 4-4c.93 0 1.79.32 2.47.86l1.42-1.42C14.84 6.67 13.49 6 12 6z"
              fill="white"
            />
            <circle cx="12" cy="12" r="2" fill="white" />
          </svg>
        </Box>
      </Box>
      <Typography
        variant="h4"
        sx={{ color: '#ffffff', fontWeight: 700, fontSize: '2rem', lineHeight: 1.2 }}
      >
        Your Ai Heath Assistant
      </Typography>
    </CardContent>
  </Card>
);

const LiveSubtitlesCard = () => (
  <Card
    sx={{
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      borderRadius: '20px',
      border: '0.856px solid rgba(255,255,255,0.05)',
      background:
        'radial-gradient(146.4% 139.7% at 97.54% 0%, rgba(58, 66, 216, 0.30) 0%, rgba(58, 66, 216, 0.00) 100%), #191934',
      backgroundBlendMode: 'overlay, normal, normal',
      height: '100%',
      position: 'relative',
      overflow: 'hidden',
    }}
  >
    <CardContent
      sx={{
        p: 3,
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between',
      }}
    >
      <Box
        sx={{
          position: 'absolute',
          top: 12,
          right: 12,
          width: 8,
          height: 8,
          borderRadius: '50%',
          backgroundColor: '#6366f1',
          boxShadow: '0 0 8px rgba(99, 102, 241, 0.6)',
        }}
      />
      <Typography
        variant="h6"
        sx={{ color: '#ffffff', fontWeight: 600, fontSize: '1.125rem', lineHeight: 1.4 }}
      >
        Live Subtitles for Doctors & Patients
      </Typography>
      <Box sx={{ position: 'absolute', bottom: 16, right: 16 }}>
        <Box
          sx={{
            width: 60,
            height: 40,
            borderRadius: '8px',
            background: 'rgba(255, 255, 255, 0.1)',
            backgroundImage:
              'url(https://images.unsplash.com/photo-**********-2b71ea197ec2?w=60&h=40&fit=crop&crop=face)',
            backgroundSize: 'cover',
            backgroundPosition: 'center',
          }}
        />
      </Box>
    </CardContent>
  </Card>
);

const ProblemCard = () => (
  <Card
    sx={{
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      borderRadius: '20px',
      border: '0.856px solid rgba(255,255,255,0.05)',
      background:
        'radial-gradient(146.4% 139.7% at 97.54% 0%, rgba(58, 66, 216, 0.30) 0%, rgba(58, 66, 216, 0.00) 100%), #191934',
      backgroundBlendMode: 'overlay, normal, normal',
      height: '100%',
      position: 'relative',
      overflow: 'hidden',
    }}
  >
    <CardContent
      sx={{
        p: 3,
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
      }}
    >
      <Typography
        variant="h5"
        sx={{
          color: '#ffffff',
          fontWeight: 600,
          fontSize: '1.5rem',
          lineHeight: 1.4,
          textAlign: 'center',
        }}
      >
        Just the Say your Problem
      </Typography>
      <Typography
        variant="body2"
        sx={{
          color: 'rgba(255, 255, 255, 0.7)',
          fontSize: '0.875rem',
          fontWeight: 400,
          textAlign: 'center',
          mt: 1,
        }}
      >
        Our doctors will be available right away.
      </Typography>
    </CardContent>
  </Card>
);

const VoiceTranslationCard = () => (
  <Card
    sx={{
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      borderRadius: '20px',
      border: '0.856px solid rgba(255,255,255,0.05)',
      background:
        'radial-gradient(146.4% 139.7% at 97.54% 0%, rgba(58, 66, 216, 0.30) 0%, rgba(58, 66, 216, 0.00) 100%), #191934',
      backgroundBlendMode: 'overlay, normal, normal',
      height: '100%',
      position: 'relative',
      overflow: 'hidden',
    }}
  >
    <CardContent
      sx={{
        p: 3,
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        textAlign: 'center',
      }}
    >
      <Box
        sx={{
          position: 'absolute',
          top: 12,
          right: 12,
          width: 8,
          height: 8,
          borderRadius: '50%',
          backgroundColor: '#6366f1',
          boxShadow: '0 0 8px rgba(99, 102, 241, 0.6)',
        }}
      />
      <Typography
        variant="h6"
        sx={{ color: '#ffffff', fontWeight: 600, fontSize: '1.125rem', lineHeight: 1.4, mb: 1 }}
      >
        Real-Time Voice Translation
      </Typography>
      <Typography
        variant="body2"
        sx={{ color: 'rgba(255, 255, 255, 0.7)', fontSize: '0.875rem', fontWeight: 400 }}
      >
        Doctor-Patient Conversations
      </Typography>
    </CardContent>
  </Card>
);

const InsuranceCard = () => (
  <Card
    sx={{
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      borderRadius: '20px',
      border: '0.856px solid rgba(255,255,255,0.05)',
      background:
        'radial-gradient(146.4% 139.7% at 97.54% 0%, rgba(58, 66, 216, 0.30) 0%, rgba(58, 66, 216, 0.00) 100%), #191934',
      backgroundBlendMode: 'overlay, normal, normal',
      height: '100%',
      position: 'relative',
      overflow: 'hidden',
    }}
  >
    <CardContent
      sx={{
        p: 3,
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between',
      }}
    >
      <Typography
        variant="h6"
        sx={{ color: '#ffffff', fontWeight: 600, fontSize: '1.125rem', lineHeight: 1.4 }}
      >
        Insurance of the contracting party
      </Typography>

      {/* Large 45 indicator positioned like in the design */}
      <Box sx={{ position: 'absolute', bottom: 16, right: 16 }}>
        <Box
          sx={{
            width: 80,
            height: 80,
            borderRadius: '50%',
            background: 'conic-gradient(from 0deg, #f59e0b, #fbbf24, #f59e0b)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            position: 'relative',
            '&::before': {
              content: '""',
              position: 'absolute',
              inset: 4,
              borderRadius: '50%',
              background: '#2a2d3a',
            },
          }}
        >
          <Typography
            variant="h4"
            sx={{
              color: '#f59e0b',
              fontWeight: 700,
              fontSize: '1.5rem',
              position: 'relative',
              zIndex: 1,
            }}
          >
            45
          </Typography>
        </Box>
      </Box>
    </CardContent>
  </Card>
);

const ChatInput = () => {
  const [message, setMessage] = useState('');

  return (
    <Card
      sx={{
        background: 'linear-gradient(135deg, #2a2d3a 0%, #1e2028 100%)',
        border: '1px solid rgba(255, 255, 255, 0.1)',
        borderRadius: '16px',
        position: 'fixed',
        bottom: 24,
        left: { xs: 24, lg: '300px' },
        right: 24,
        maxWidth: '800px',
        mx: { lg: 'auto' },
        zIndex: 1000,
      }}
    >
      <CardContent sx={{ p: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <IconButton size="small" sx={{ color: 'rgba(255, 255, 255, 0.7)' }}>
            <MicIcon fontSize="small" />
          </IconButton>
          <Box
            component="input"
            placeholder="Type message"
            value={message}
            onChange={(e: any) => setMessage(e.target.value)}
            sx={{
              flex: 1,
              border: 'none',
              outline: 'none',
              background: 'transparent',
              color: '#ffffff',
              fontSize: '0.875rem',
              '&::placeholder': {
                color: 'rgba(255, 255, 255, 0.5)',
              },
            }}
          />
          <IconButton size="small" sx={{ color: 'rgba(255, 255, 255, 0.7)' }}>
            <AttachFileIcon fontSize="small" />
          </IconButton>
          <IconButton size="small" sx={{ color: message ? '#6366f1' : 'rgba(255, 255, 255, 0.3)' }}>
            <SendIcon fontSize="small" />
          </IconButton>
        </Box>
      </CardContent>
    </Card>
  );
};

export default function MediPathHomeView() {
  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: 'linear-gradient(180deg, #1a1d29 0%, #0f1117 100%)',
        pb: 12,
      }}
    >
      <Container maxWidth="xl" sx={{ py: 3 }}>
        <Box
          sx={{
            display: 'grid',
            gridTemplateColumns: '3fr 1fr',
            gap: 2,
            height: 'calc(100vh - 200px)',
          }}
        >
          {/* Main Grid 9 - Left Side */}
          <Box sx={{ display: 'grid', gridTemplateRows: '1fr 1fr', gap: 2 }}>
            {/* Top Row - Grid 3 + Grid 9 */}
            <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 3fr', gap: 2 }}>
              {/* Grid 3 - Stats Cards Column */}
              <Box sx={{ display: 'grid', gridTemplateRows: '1fr 1fr', gap: 2 }}>
                <StatsCard value="2.5k" label="Expert Doctors" />
                <StatsCard value="12K" label="happy users" avatars={userAvatars} />
              </Box>

              {/* Grid 9 - Hero Card */}
              <HeroCard />
            </Box>

            {/* Bottom Row - Grid 9 + Grid 3 */}
            <Box sx={{ display: 'grid', gridTemplateColumns: '3fr 1fr', gap: 2 }}>
              {/* Grid 9 - Problem Card */}
              <ProblemCard />

              {/* Grid 3 - Voice Translation */}
              <VoiceTranslationCard />
            </Box>
          </Box>

          {/* Right Grid 3 - 75% + 25% */}
          <Box sx={{ display: 'grid', gridTemplateRows: '3fr 1fr', gap: 2 }}>
            {/* 75% - Live Subtitles */}
            <LiveSubtitlesCard />

            {/* 25% - Insurance */}
            <InsuranceCard />
          </Box>
        </Box>
      </Container>

      <ChatInput />
    </Box>
  );
}
