<svg width="201" height="200" viewBox="0 0 201 200" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_di_1587_120562)">
<path d="M145.141 155.648L96.0227 129.617C94.7426 128.937 93.1816 129.031 91.9718 129.851L54.4132 155.883C53.0083 156.859 52.3995 158.633 52.9146 160.266C53.4142 161.883 54.9284 163 56.6299 163H143.306C145.094 163 146.663 161.781 147.092 160.031C147.529 158.289 146.717 156.484 145.141 155.648Z" fill="white"/>
</g>
<g filter="url(#filter1_di_1587_120562)">
<path d="M134.4 87.6172C133.885 85.9844 132.379 84.875 130.67 84.875H58.5262C56.6998 84.875 55.1154 86.1484 54.7173 87.9375L41.5968 147.023C41.2534 148.594 41.9012 150.211 43.2281 151.109C43.8837 151.555 44.6486 151.773 45.4057 151.773C46.1862 151.773 46.959 151.547 47.6302 151.078L132.886 91.9922C134.299 91.0234 134.9 89.25 134.4 87.6172Z" fill="#00A76F"/>
</g>
<path d="M150.027 96.172C149.746 94.8985 148.848 93.8595 147.638 93.3829C146.421 92.9063 145.055 93.0704 143.993 93.8048L106.427 119.836C105.303 120.617 104.671 121.922 104.757 123.274C104.835 124.641 105.623 125.859 106.825 126.492L155.935 152.524C156.513 152.828 157.137 152.984 157.762 152.984C158.597 152.984 159.432 152.719 160.127 152.18C161.337 151.258 161.906 149.719 161.579 148.227L150.027 96.172Z" fill="white"/>
<path opacity="0.24" d="M150.027 96.172C149.746 94.8985 148.848 93.8595 147.638 93.3829C146.421 92.9063 145.055 93.0704 143.993 93.8048L106.427 119.836C105.303 120.617 104.671 121.922 104.757 123.274C104.835 124.641 105.623 125.859 106.825 126.492L155.935 152.524C156.513 152.828 157.137 152.984 157.762 152.984C158.597 152.984 159.432 152.719 160.127 152.18C161.337 151.258 161.906 149.719 161.579 148.227L150.027 96.172Z" fill="#007867"/>
<g filter="url(#filter2_di_1587_120562)">
<path d="M66.3318 38C81.3958 38 93.65 50.2656 93.65 65.3438C93.65 79.375 71.733 104.227 69.2353 107.016C68.4939 107.836 67.4402 108.312 66.3318 108.312C65.2235 108.312 64.1698 107.836 63.4283 107.016C60.9306 104.227 39.0137 79.375 39.0137 65.3438C39.0137 50.2656 51.2678 38 66.3318 38ZM66.3318 53.625C59.8658 53.625 54.624 58.8717 54.624 65.3438C54.624 71.8158 59.8658 77.0625 66.3318 77.0625C72.7978 77.0625 78.0396 71.8158 78.0396 65.3438C78.0396 58.8717 72.7978 53.625 66.3318 53.625Z" fill="#FFAB00"/>
</g>
<defs>
<filter id="filter0_di_1587_120562" x="44.7334" y="121.165" width="126.476" height="65.835" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="8" dy="8"/>
<feGaussianBlur stdDeviation="8"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.770709 0 0 0 0 0.792653 0 0 0 0 0.818587 0 0 0 0.16 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1587_120562"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1587_120562" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-2" dy="-2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.717499 0 0 0 0 0.740813 0 0 0 0 0.768367 0 0 0 0.48 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1587_120562"/>
</filter>
<filter id="filter1_di_1587_120562" x="33.5059" y="76.875" width="125.068" height="98.8984" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="8" dy="8"/>
<feGaussianBlur stdDeviation="8"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.470588 0 0 0 0 0.403922 0 0 0 0.16 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1587_120562"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1587_120562" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-2" dy="-2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.470588 0 0 0 0 0.403922 0 0 0 0.48 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1587_120562"/>
</filter>
<filter id="filter2_di_1587_120562" x="31.0137" y="30" width="86.6362" height="102.312" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="8" dy="8"/>
<feGaussianBlur stdDeviation="8"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.717647 0 0 0 0 0.431373 0 0 0 0 0 0 0 0 0.16 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1587_120562"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1587_120562" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-2" dy="-2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.717647 0 0 0 0 0.431373 0 0 0 0 0 0 0 0 0.48 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1587_120562"/>
</filter>
</defs>
</svg>
