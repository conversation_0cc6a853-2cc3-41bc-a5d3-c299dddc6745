<svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1587_120556)">
<g filter="url(#filter0_di_1587_120556)">
<rect x="78" y="39" width="44" height="44" rx="10" fill="#00A76F"/>
</g>
<g filter="url(#filter1_di_1587_120556)">
<path d="M99.9998 61.2128C102.992 61.2128 105.419 58.9266 105.419 56.1064C105.419 53.2862 102.992 51 99.9998 51C97.0071 51 94.5811 53.2862 94.5811 56.1064C94.5811 58.9266 97.0071 61.2128 99.9998 61.2128ZM96.2722 62.8019C98.6972 62.0449 101.302 62.0449 103.727 62.8019C106.111 63.546 108.087 65.2006 109.207 67.3909L109.819 68.5868C110.24 69.4097 109.9 70.4111 109.06 70.8234C108.823 70.9395 108.562 71 108.298 71H91.702C90.762 71 90 70.2538 90 69.3333C90 69.0741 90.0617 68.8185 90.1803 68.5868L90.7921 67.3909C91.9128 65.2006 93.8887 63.546 96.2722 62.8019Z" fill="white"/>
</g>
<path d="M161 117V100.717C161 98.8157 159.761 97.548 157.904 97.548H103.096V83H96.9036V97.548H42.0964C40.2386 97.548 39 98.8157 39 100.717V117H45.1929V103.886H96.9036V117H103.096V103.886H154.807V117H161Z" fill="white"/>
<path opacity="0.12" d="M161 117V100.717C161 98.8157 159.761 97.548 157.904 97.548H103.096V83H96.9036V97.548H42.0964C40.2386 97.548 39 98.8157 39 100.717V117H45.1929V103.886H96.9036V117H103.096V103.886H154.807V117H161Z" fill="#007867"/>
<g filter="url(#filter2_di_1587_120556)">
<rect x="19" y="117" width="44" height="44" rx="10" fill="white"/>
</g>
<g filter="url(#filter3_di_1587_120556)">
<rect x="78" y="117" width="44" height="44" rx="10" fill="#FFAB00"/>
</g>
<g filter="url(#filter4_di_1587_120556)">
<rect x="137" y="117" width="44" height="44" rx="10" fill="white"/>
</g>
</g>
<defs>
<filter id="filter0_di_1587_120556" x="70" y="31" width="76" height="76" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="8" dy="8"/>
<feGaussianBlur stdDeviation="8"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.470588 0 0 0 0 0.403922 0 0 0 0.16 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1587_120556"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1587_120556" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-2" dy="-2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.470588 0 0 0 0 0.403922 0 0 0 0.48 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1587_120556"/>
</filter>
<filter id="filter1_di_1587_120556" x="86" y="47" width="36" height="36" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="4" dy="4"/>
<feGaussianBlur stdDeviation="4"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.568627 0 0 0 0 0.619608 0 0 0 0 0.670588 0 0 0 0.16 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1587_120556"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1587_120556" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1" dy="-1"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.568627 0 0 0 0 0.619608 0 0 0 0 0.670588 0 0 0 0.48 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1587_120556"/>
</filter>
<filter id="filter2_di_1587_120556" x="11" y="109" width="76" height="76" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="8" dy="8"/>
<feGaussianBlur stdDeviation="8"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.770709 0 0 0 0 0.792653 0 0 0 0 0.818587 0 0 0 0.16 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1587_120556"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1587_120556" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-2" dy="-2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.717499 0 0 0 0 0.740813 0 0 0 0 0.768367 0 0 0 0.48 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1587_120556"/>
</filter>
<filter id="filter3_di_1587_120556" x="70" y="109" width="76" height="76" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="8" dy="8"/>
<feGaussianBlur stdDeviation="8"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.717647 0 0 0 0 0.431373 0 0 0 0 0 0 0 0 0.16 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1587_120556"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1587_120556" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-2" dy="-2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.717647 0 0 0 0 0.431373 0 0 0 0 0 0 0 0 0.48 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1587_120556"/>
</filter>
<filter id="filter4_di_1587_120556" x="129" y="109" width="76" height="76" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="8" dy="8"/>
<feGaussianBlur stdDeviation="8"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.770709 0 0 0 0 0.792653 0 0 0 0 0.818587 0 0 0 0.16 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1587_120556"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1587_120556" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-2" dy="-2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.717499 0 0 0 0 0.740813 0 0 0 0 0.768367 0 0 0 0.48 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1587_120556"/>
</filter>
<clipPath id="clip0_1587_120556">
<rect width="200" height="200" fill="white"/>
</clipPath>
</defs>
</svg>
