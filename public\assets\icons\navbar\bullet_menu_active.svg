<svg width="52" height="52" viewBox="0 0 52 52" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_f_541_3031)">
<circle cx="26" cy="26" r="6" fill="url(#paint0_radial_541_3031)"/>
<circle cx="26" cy="26" r="6" fill="url(#paint1_radial_541_3031)"/>
</g>
<g filter="url(#filter1_ii_541_3031)">
<circle cx="27" cy="26" r="7" fill="url(#paint2_radial_541_3031)"/>
<circle cx="27" cy="26" r="7" fill="url(#paint3_radial_541_3031)"/>
</g>
<defs>
<filter id="filter0_f_541_3031" x="0" y="0" width="52" height="52" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="10" result="effect1_foregroundBlur_541_3031"/>
</filter>
<filter id="filter1_ii_541_3031" x="20" y="17" width="14" height="18" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.014184 0 0 0 0 0.0185896 0 0 0 0 0.0791667 0 0 0 0.18 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_541_3031"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.014184 0 0 0 0 0.0185896 0 0 0 0 0.0791667 0 0 0 0.18 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_541_3031" result="effect2_innerShadow_541_3031"/>
</filter>
<radialGradient id="paint0_radial_541_3031" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(24.5 23.75) rotate(52.125) scale(8.55132)">
<stop stop-color="#B2A1FD"/>
<stop offset="1" stop-color="#3A42D8"/>
</radialGradient>
<radialGradient id="paint1_radial_541_3031" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(24.5 23.75) rotate(52.125) scale(8.55132)">
<stop stop-color="#B2A1FD"/>
<stop offset="1" stop-color="#3A42D8"/>
</radialGradient>
<radialGradient id="paint2_radial_541_3031" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(25.25 23.375) rotate(52.125) scale(9.97654)">
<stop stop-color="#B2A1FD"/>
<stop offset="1" stop-color="#3A42D8"/>
</radialGradient>
<radialGradient id="paint3_radial_541_3031" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(25.25 23.375) rotate(52.125) scale(9.97654)">
<stop stop-color="#B2A1FD"/>
<stop offset="1" stop-color="#3A42D8"/>
</radialGradient>
</defs>
</svg>
